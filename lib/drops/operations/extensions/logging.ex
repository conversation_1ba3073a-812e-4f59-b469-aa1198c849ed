defmodule Drops.Operations.Extensions.Logging do
  @moduledoc """
  Logging extension for Operations framework.

  This extension provides configurable logging for Operations by leveraging telemetry events
  and a custom log handler. It supports both debug mode (detailed logging with context) and
  info mode (basic success/error logging) with configurable step filtering.

  ## Features

  - Configurable logging levels (debug mode vs info mode)
  - Automatic operation-level logging (start/stop events)
  - Configurable step-level instrumentation
  - Configurable log handler (console, file, or memory)
  - Metadata includes operation module, step name, and execution context
  - Built on top of the Telemetry extension
  - Smart metadata dumping for complex types via MetadataDumper protocol

  ## Configuration

  The logging handler can be configured via application environment:

      config :drops, :logger,
        handler: :file,
        file: "log/operations.log",
        level: :debug,
        format: "[$level] $message $metadata\\n",
        metadata: [:operation, :step]

  ## Usage

  ### Enable Basic Logging

  Enable default logging with info level (no context included):

      defmodule CreateUser do
        use Drops.Operations.Command, logging: true

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  ### Enable Debug Logging

  Enable debug logging with detailed context:

      defmodule CreateUser do
        use Drops.Operations.Command, debug: true

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  ### Step-Level Configuration

  Configure logging for specific steps:

      defmodule CreateUser do
        use Drops.Operations.Command, logging: [steps: [:validate, :execute]]

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  Configure logging for all steps:

      defmodule CreateUser do
        use Drops.Operations.Command, logging: [steps: :all]

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  ## Logging Behavior

  ### Debug Mode (debug: true)

  - Logs at debug level for successful operations/steps
  - Logs at error level for failed operations/steps
  - Includes full context in all log messages
  - Logs all steps by default

  ### Info Mode (logging: true or logging: [steps: ...])

  - Logs at info level for successful operations/steps
  - Logs at error level for failed operations/steps
  - No context included for successful operations
  - Context included for failed operations
  - Configurable step filtering

  ## Implementation Details

  This extension works by:

  1. Enabling telemetry with both operation and step-level instrumentation
  2. Attaching telemetry handlers that log events using the custom logging handler
  3. Automatically cleaning up handlers when the operation module is unloaded

  The extension uses the Telemetry extension internally and attaches handlers
  during module compilation.
  """
  use Drops.Operations.Extension

  require Logger

  @depends_on [Drops.Operations.Extensions.Telemetry]

  @impl true
  @spec enable?(keyword()) :: boolean()
  def enable?(opts) do
    # Enable if either logging or debug is configured
    case {Keyword.get(opts, :logging, false), Keyword.get(opts, :debug, false)} do
      {false, false} -> false
      # debug: true takes precedence
      {_, true} -> true
      {_, config} when is_list(config) -> true
      {true, _} -> true
      {config, _} when is_list(config) -> true
      _ -> false
    end
  end

  @impl true
  @spec default_opts(keyword()) :: keyword()
  def default_opts(opts) do
    debug_config = Keyword.get(opts, :debug, false)
    logging_config = Keyword.get(opts, :logging, false)

    cond do
      debug_config != false ->
        # Debug mode: enable telemetry with all steps instrumented
        case debug_config do
          true ->
            [telemetry: [steps: :all]]

          config when is_list(config) ->
            # Pass through custom identifier but ensure all steps are instrumented
            identifier = Keyword.get(config, :identifier, :drops)
            [telemetry: [identifier: identifier, steps: :all]]
        end

      logging_config != false ->
        # Logging mode: configure telemetry based on logging config
        case logging_config do
          true ->
            # Default logging: instrument operation boundaries only
            [telemetry: true]

          config when is_list(config) ->
            # Custom logging configuration
            identifier = Keyword.get(config, :identifier, :drops)
            steps = Keyword.get(config, :steps, [])

            case steps do
              :all ->
                [telemetry: [identifier: identifier, steps: :all]]

              [] ->
                [telemetry: [identifier: identifier]]

              step_list when is_list(step_list) ->
                [telemetry: [identifier: identifier, steps: step_list]]
            end
        end

      true ->
        []
    end
  end

  @impl true
  @spec unit_of_work(Drops.Operations.UnitOfWork.t(), keyword()) ::
          Drops.Operations.UnitOfWork.t()
  def unit_of_work(uow, opts) do
    debug_config = Keyword.get(opts, :debug, false)
    logging_config = Keyword.get(opts, :logging, false)

    # Determine which config to use (debug takes precedence)
    config =
      if debug_config != false,
        do: {:debug, debug_config},
        else: {:logging, logging_config}

    case config do
      {_, false} ->
        uow

      {mode, config_value} ->
        # Store logging config in module attribute for later use in __before_compile__
        Module.put_attribute(uow.module, :drops_logging_config, {mode, config_value})

        # Add a before callback to the first step to ensure handlers are attached
        first_step = List.first(uow.step_order)

        if first_step do
          Drops.Operations.UnitOfWork.register_before_callback(
            uow,
            first_step,
            __MODULE__,
            :ensure_logging_handlers_attached,
            %{logging_config: {mode, config_value}}
          )
        else
          uow
        end
    end
  end

  @impl true
  @spec using() :: Macro.t()
  def using do
    quote do
      # Ensure handlers are cleaned up when module is unloaded
      @before_compile Drops.Operations.Extensions.Logging
    end
  end

  @impl true
  @spec helpers() :: Macro.t()
  def helpers do
    quote do
      # No additional helpers needed
    end
  end

  @impl true
  @spec steps() :: Macro.t()
  def steps do
    quote do
      # No additional steps needed
    end
  end

  defmacro __before_compile__(env) do
    # Get the logging configuration from the module attributes
    logging_config = Module.get_attribute(env.module, :drops_logging_config)

    if logging_config do
      quote do
        def __logging_handler_id__, do: "logging-#{__MODULE__}"

        # Clean up handlers when module is unloaded
        @before_compile :__detach_logging_handlers__

        def __detach_logging_handlers__ do
          try do
            :telemetry.detach(__logging_handler_id__())
          rescue
            _ -> :ok
          end
        end
      end
    else
      quote do
        # No logging configuration, no handlers to manage
      end
    end
  end

  # Private functions

  @doc false
  def ensure_logging_handlers_attached(operation_module, _step, _context, config) do
    logging_config = config.logging_config

    # Try to attach handlers if not already attached
    try do
      attach_logging_handlers(operation_module, logging_config)
    rescue
      _ -> :ok
    end

    :ok
  end

  @doc false
  def attach_logging_handlers(operation_module, logging_config) do
    identifier = get_identifier(logging_config)
    handler_id = "logging-#{operation_module}"

    # Define the events we want to listen to
    events = [
      [identifier, :operation, :start],
      [identifier, :operation, :stop],
      [identifier, :operation, :exception],
      [identifier, :operation, :step, :start],
      [identifier, :operation, :step, :stop],
      [identifier, :operation, :step, :exception]
    ]

    # Attach the handler
    :telemetry.attach_many(
      handler_id,
      events,
      &__MODULE__.handle_logging_event/4,
      %{operation_module: operation_module, logging_config: logging_config}
    )
  end

  defp get_identifier(logging_config) do
    case logging_config do
      {:debug, true} -> :drops
      {:debug, config} when is_list(config) -> Keyword.get(config, :identifier, :drops)
      {:logging, true} -> :drops
      {:logging, config} when is_list(config) -> Keyword.get(config, :identifier, :drops)
      true -> :drops
      config when is_list(config) -> Keyword.get(config, :identifier, :drops)
      _ -> :drops
    end
  end

  defp get_logging_config(logging_config) do
    case logging_config do
      {:debug, _} ->
        # Debug mode: debug level for success, error level for failures, always include context
        {:debug, true}

      {:logging, _} ->
        # Info mode: info level for success, error level for failures, no context for success
        {:info, false}

      _ ->
        # Fallback to debug mode
        {:debug, true}
    end
  end

  @doc false
  def handle_logging_event(
        [_identifier, :operation, :start],
        measurements,
        metadata,
        config
      ) do
    operation_name = format_operation_name(metadata.operation)
    {log_level, include_context} = get_logging_config(config.logging_config)

    log_metadata = [
      operation: operation_name,
      step: metadata.step,
      system_time: measurements.system_time
    ]

    log_metadata =
      if include_context do
        context = format_context_for_logging(metadata.context)
        Keyword.put(log_metadata, :context, context)
      else
        log_metadata
      end

    Logger.log(log_level, "#{operation_name} started", log_metadata)
  end

  def handle_logging_event(
        [_identifier, :operation, :stop],
        measurements,
        metadata,
        config
      ) do
    operation_name = format_operation_name(metadata.operation)
    duration_us = System.convert_time_unit(measurements.duration, :native, :microsecond)
    duration_display = format_duration(duration_us)
    {log_level, include_context} = get_logging_config(config.logging_config)

    log_metadata = [
      operation: operation_name,
      step: metadata.step,
      duration_us: duration_us
    ]

    log_metadata =
      if include_context do
        context = format_context_for_logging(metadata.context)
        Keyword.put(log_metadata, :context, context)
      else
        log_metadata
      end

    Logger.log(
      log_level,
      "#{operation_name} succeeded in #{duration_display}",
      log_metadata
    )
  end

  def handle_logging_event(
        [_identifier, :operation, :exception],
        measurements,
        metadata,
        _config
      ) do
    operation_name = format_operation_name(metadata.operation)
    duration_us = System.convert_time_unit(measurements.duration, :native, :microsecond)
    duration_display = format_duration(duration_us)
    reason_info = format_reason(metadata.reason)

    # Always include context for error cases and always use error level
    context = format_context_for_logging(metadata.context)

    log_metadata =
      [
        operation: operation_name,
        step: metadata.step,
        context: context,
        duration_us: duration_us,
        kind: metadata.kind
      ] ++ reason_info

    Logger.error(
      "#{operation_name} failed in #{duration_display}",
      log_metadata
    )
  end

  def handle_logging_event(
        [_identifier, :operation, :step, :start],
        measurements,
        metadata,
        config
      ) do
    operation_name = format_operation_name(metadata.operation)
    {log_level, include_context} = get_logging_config(config.logging_config)

    log_metadata = [
      operation: operation_name,
      step: metadata.step,
      system_time: measurements.system_time
    ]

    log_metadata =
      if include_context do
        context = format_context_for_logging(metadata.context)
        Keyword.put(log_metadata, :context, context)
      else
        log_metadata
      end

    Logger.log(log_level, "#{operation_name}.#{metadata.step} started", log_metadata)
  end

  def handle_logging_event(
        [_identifier, :operation, :step, :stop],
        measurements,
        metadata,
        config
      ) do
    operation_name = format_operation_name(metadata.operation)
    duration_us = System.convert_time_unit(measurements.duration, :native, :microsecond)
    duration_display = format_duration(duration_us)
    {log_level, include_context} = get_logging_config(config.logging_config)

    log_metadata = [
      operation: operation_name,
      step: metadata.step,
      duration_us: duration_us
    ]

    log_metadata =
      if include_context do
        context = format_context_for_logging(metadata.context)
        Keyword.put(log_metadata, :context, context)
      else
        log_metadata
      end

    Logger.log(
      log_level,
      "#{operation_name}.#{metadata.step} succeeded in #{duration_display}",
      log_metadata
    )
  end

  def handle_logging_event(
        [_identifier, :operation, :step, :exception],
        measurements,
        metadata,
        _config
      ) do
    operation_name = format_operation_name(metadata.operation)
    duration_us = System.convert_time_unit(measurements.duration, :native, :microsecond)
    duration_display = format_duration(duration_us)
    reason_info = format_reason(metadata.reason)

    # Always include context for error cases and always use error level
    context = format_context_for_logging(metadata.context)

    log_metadata =
      [
        operation: operation_name,
        step: metadata.step,
        context: context,
        duration_us: duration_us,
        kind: metadata.kind
      ] ++ reason_info

    Logger.error(
      "#{operation_name}.#{metadata.step} failed in #{duration_display}",
      log_metadata
    )
  end

  # Private helper functions

  defp format_operation_name(operation) when is_atom(operation) do
    operation
    |> to_string()
    |> String.replace_prefix("Elixir.", "")
  end

  defp format_operation_name(operation), do: to_string(operation)

  defp format_context_for_logging(context) do
    # Pass the raw context to the logger formatter
    # The formatter will handle pretty-printing using inspect with proper options
    context
  end

  defp format_metadata_value(value) do
    try do
      Drops.Operations.Extensions.Logging.MetadataDumper.dump(value)
    rescue
      Protocol.UndefinedError ->
        # Fallback to inspect for types without MetadataDumper implementation
        inspect(value, limit: 50, printable_limit: 100)
    end
  end

  defp format_reason(reason) do
    cond do
      # Handle Ecto.Changeset errors specially
      is_struct(reason, Ecto.Changeset) and Code.ensure_loaded?(Ecto.Changeset) ->
        [
          reason: :validation,
          error_type: "Ecto.Changeset",
          errors: format_changeset_errors(reason.errors)
        ]

      # Handle other structs with errors field
      is_struct(reason) and Map.has_key?(reason, :errors) ->
        struct_name =
          reason.__struct__ |> to_string() |> String.replace_prefix("Elixir.", "")

        [
          reason: :error,
          error_type: struct_name,
          errors: format_metadata_value(reason.errors)
        ]

      # Handle regular values
      true ->
        [reason: format_metadata_value(reason)]
    end
  end

  defp format_changeset_errors(errors) when is_list(errors) do
    errors
    |> Enum.map(fn {field, {message, _opts}} ->
      "#{field}: #{message}"
    end)
    |> Enum.join(", ")
  end

  defp format_changeset_errors(errors), do: format_metadata_value(errors)

  defp format_duration(duration_us) when duration_us < 0 do
    # Handle negative durations (should not happen, but defensive programming)
    "#{duration_us}μs (invalid)"
  end

  defp format_duration(duration_us) when duration_us < 1000, do: "#{duration_us}μs"

  defp format_duration(duration_us) when duration_us < 1_000_000,
    do: "#{Float.round(duration_us / 1000, 2)}ms"

  defp format_duration(duration_us), do: "#{Float.round(duration_us / 1_000_000, 2)}s"
end
